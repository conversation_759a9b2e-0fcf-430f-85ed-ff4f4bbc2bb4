{"name": "planit-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "Frontend for Planit - Collaborative Trip Planner", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "lucide-react": "^0.344.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "@headlessui/react": "^1.7.17", "framer-motion": "^10.16.16"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}, "keywords": ["react", "typescript", "vite", "trip-planner", "collaborative", "travel"], "author": "Planit Team", "license": "MIT"}