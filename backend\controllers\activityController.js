const { validationResult } = require('express-validator');
const Activity = require('../models/Activity');
const Trip = require('../models/Trip');
const Message = require('../models/Message');

/**
 * Get activities for a trip
 * GET /api/trips/:tripId/activities
 */
const getActivities = async (req, res) => {
  try {
    const { tripId } = req.params;
    const { category, status, date, page = 1, limit = 50 } = req.query;
    
    const skip = (page - 1) * limit;
    const query = { trip: tripId };
    
    if (category) query.category = category;
    if (status) query.status = status;
    if (date) {
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);
      query.date = { $gte: startOfDay, $lte: endOfDay };
    }
    
    const activities = await Activity.find(query)
      .populate('creator', 'name email profilePhoto')
      .populate('votes.user', 'name email profilePhoto')
      .sort({ date: 1, order: 1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    const total = await Activity.countDocuments(query);
    
    res.json({
      success: true,
      data: {
        activities,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total
        }
      }
    });
    
  } catch (error) {
    console.error('Get activities error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching activities'
    });
  }
};

/**
 * Create a new activity
 * POST /api/trips/:tripId/activities
 */
const createActivity = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const { tripId } = req.params;
    const {
      title,
      description,
      date,
      time,
      location,
      category,
      cost,
      priority,
      tags,
      notes
    } = req.body;
    
    const activity = new Activity({
      title: title.trim(),
      description: description?.trim() || '',
      trip: tripId,
      creator: req.user._id,
      date: new Date(date),
      time: time || {},
      location: location || {},
      category,
      cost: cost || { amount: 0, currency: 'USD', perPerson: true },
      priority: priority || 'medium',
      tags: tags || [],
      notes: notes?.trim() || ''
    });
    
    await activity.save();
    
    // Add activity to trip
    await Trip.findByIdAndUpdate(tripId, {
      $push: { activities: activity._id },
      lastActivity: new Date()
    });
    
    // Create system message
    await Message.createSystemMessage(tripId, 'activity_added', {
      userName: req.user.name,
      activityTitle: title
    });
    
    const populatedActivity = await Activity.findById(activity._id)
      .populate('creator', 'name email profilePhoto')
      .populate('votes.user', 'name email profilePhoto');
    
    res.status(201).json({
      success: true,
      message: 'Activity created successfully',
      data: {
        activity: populatedActivity
      }
    });
    
  } catch (error) {
    console.error('Create activity error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating activity'
    });
  }
};

/**
 * Update an activity
 * PUT /api/activities/:id
 */
const updateActivity = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const { id } = req.params;
    const updateData = req.body;
    
    const activity = await Activity.findById(id);
    if (!activity) {
      return res.status(404).json({
        success: false,
        message: 'Activity not found'
      });
    }
    
    // Check if user can edit this activity
    const isCreator = activity.creator.toString() === req.user._id.toString();
    const trip = await Trip.findById(activity.trip);
    const canEdit = trip.canUserEdit(req.user._id);
    
    if (!isCreator && !canEdit) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to edit this activity'
      });
    }
    
    const updatedActivity = await Activity.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    )
      .populate('creator', 'name email profilePhoto')
      .populate('votes.user', 'name email profilePhoto');
    
    // Update trip's last activity
    await Trip.findByIdAndUpdate(activity.trip, {
      lastActivity: new Date()
    });
    
    res.json({
      success: true,
      message: 'Activity updated successfully',
      data: {
        activity: updatedActivity
      }
    });
    
  } catch (error) {
    console.error('Update activity error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating activity'
    });
  }
};

/**
 * Delete an activity
 * DELETE /api/activities/:id
 */
const deleteActivity = async (req, res) => {
  try {
    const { id } = req.params;
    
    const activity = await Activity.findById(id);
    if (!activity) {
      return res.status(404).json({
        success: false,
        message: 'Activity not found'
      });
    }
    
    // Check if user can delete this activity
    const isCreator = activity.creator.toString() === req.user._id.toString();
    const trip = await Trip.findById(activity.trip);
    const canEdit = trip.canUserEdit(req.user._id);
    
    if (!isCreator && !canEdit) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to delete this activity'
      });
    }
    
    await Activity.findByIdAndDelete(id);
    
    // Remove activity from trip
    await Trip.findByIdAndUpdate(activity.trip, {
      $pull: { activities: id },
      lastActivity: new Date()
    });
    
    res.json({
      success: true,
      message: 'Activity deleted successfully'
    });
    
  } catch (error) {
    console.error('Delete activity error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting activity'
    });
  }
};

/**
 * Vote on an activity
 * POST /api/activities/:id/vote
 */
const voteActivity = async (req, res) => {
  try {
    const { id } = req.params;
    const { vote } = req.body; // 'up', 'down', or 'remove'
    const userId = req.user._id;
    
    if (!['up', 'down', 'remove'].includes(vote)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid vote type. Must be "up", "down", or "remove"'
      });
    }
    
    const activity = await Activity.findById(id);
    if (!activity) {
      return res.status(404).json({
        success: false,
        message: 'Activity not found'
      });
    }
    
    // Check if user is participant of the trip
    const trip = await Trip.findById(activity.trip);
    if (!trip.isParticipant(userId)) {
      return res.status(403).json({
        success: false,
        message: 'You must be a trip participant to vote'
      });
    }
    
    if (vote === 'remove') {
      activity.removeVote(userId);
    } else {
      activity.addVote(userId, vote);
    }
    
    await activity.save();
    
    // Create system message for vote
    if (vote !== 'remove') {
      await Message.createSystemMessage(activity.trip, 'activity_voted', {
        userName: req.user.name,
        activityTitle: activity.title
      });
    }
    
    const updatedActivity = await Activity.findById(id)
      .populate('creator', 'name email profilePhoto')
      .populate('votes.user', 'name email profilePhoto');
    
    res.json({
      success: true,
      message: vote === 'remove' ? 'Vote removed successfully' : 'Vote recorded successfully',
      data: {
        activity: updatedActivity
      }
    });
    
  } catch (error) {
    console.error('Vote activity error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while voting on activity'
    });
  }
};

/**
 * Get activity statistics for a trip
 * GET /api/trips/:tripId/activities/stats
 */
const getActivityStats = async (req, res) => {
  try {
    const { tripId } = req.params;
    
    const stats = await Activity.aggregate([
      { $match: { trip: mongoose.Types.ObjectId(tripId) } },
      {
        $group: {
          _id: null,
          totalActivities: { $sum: 1 },
          totalCost: { $sum: '$cost.amount' },
          byCategory: {
            $push: {
              category: '$category',
              cost: '$cost.amount'
            }
          },
          byStatus: {
            $push: '$status'
          }
        }
      },
      {
        $project: {
          totalActivities: 1,
          totalCost: 1,
          categoryBreakdown: {
            $reduce: {
              input: '$byCategory',
              initialValue: {},
              in: {
                $mergeObjects: [
                  '$$value',
                  {
                    $arrayToObject: [[{
                      k: '$$this.category',
                      v: { $add: [{ $ifNull: [{ $getField: { field: '$$this.category', input: '$$value' } }, 0] }, '$$this.cost'] }
                    }]]
                  }
                ]
              }
            }
          },
          statusBreakdown: {
            $reduce: {
              input: '$byStatus',
              initialValue: {},
              in: {
                $mergeObjects: [
                  '$$value',
                  {
                    $arrayToObject: [[{
                      k: '$$this',
                      v: { $add: [{ $ifNull: [{ $getField: { field: '$$this', input: '$$value' } }, 0] }, 1] }
                    }]]
                  }
                ]
              }
            }
          }
        }
      }
    ]);
    
    res.json({
      success: true,
      data: {
        stats: stats[0] || {
          totalActivities: 0,
          totalCost: 0,
          categoryBreakdown: {},
          statusBreakdown: {}
        }
      }
    });
    
  } catch (error) {
    console.error('Get activity stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching activity statistics'
    });
  }
};

module.exports = {
  getActivities,
  createActivity,
  updateActivity,
  deleteActivity,
  voteActivity,
  getActivityStats
};
