import React, { useState, useEffect } from 'react';
import { AuthScreen } from './components/Auth/AuthScreen';
import { Sidebar } from './components/Navigation/Sidebar';
import { HomePage } from './components/Dashboard/HomePage';
import { CreateTripPage } from './components/Trip/CreateTripPage';
import { FriendsTripsPage } from './components/Trip/FriendsTripsPage';
import { useAppState } from './hooks/useAppState';
import { auth } from './utils/auth';
import { storage } from './utils/storage';

function App() {
  const {
    currentUser,
    setCurrentUser,
    trips,
    updateTrips,
    activities,
    updateActivities,
    messages,
    updateMessages,
    users,
    updateUsers,
    isAuthenticated
  } = useAppState();

  const [currentPage, setCurrentPage] = useState('home');
  const [selectedTripId, setSelectedTripId] = useState<string | null>(null);

  useEffect(() => {
    // Check for last visited trip on load
    if (isAuthenticated) {
      const lastTripId = storage.getLastVisitedTrip();
      if (lastTripId) {
        setSelectedTripId(lastTripId);
        setCurrentPage('trip-dashboard');
      }
    }
  }, [isAuthenticated]);

  const handleAuthenticated = (user: any) => {
    setCurrentUser(user);
  };

  const handleLogout = () => {
    auth.logout();
    setCurrentUser(null);
    setCurrentPage('home');
    setSelectedTripId(null);
  };

  const handleNavigation = (page: string, tripId?: string) => {
    setCurrentPage(page);
    if (tripId) {
      setSelectedTripId(tripId);
      storage.setLastVisitedTrip(tripId);
    }
  };

  const handleCreateTrip = (trip: any) => {
    const newTrips = [...trips, trip];
    updateTrips(newTrips);
  };

  if (!isAuthenticated || !currentUser) {
    return <AuthScreen onAuthenticated={handleAuthenticated} />;
  }

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'home':
        return <HomePage currentUser={currentUser} onNavigate={handleNavigation} />;
      case 'create-trip':
        return (
          <CreateTripPage
            currentUser={currentUser}
            trips={trips}
            onCreateTrip={handleCreateTrip}
            onNavigate={handleNavigation}
          />
        );
      case 'friends-trips':
        return (
          <FriendsTripsPage
            currentUser={currentUser}
            trips={trips}
            users={users}
            onNavigate={handleNavigation}
          />
        );
      case 'budget-lineup':
        return (
          <div className="max-w-4xl mx-auto p-8">
            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">Budget Lineup</h1>
              <p className="text-gray-600">Coming soon - Track your spending across all trips</p>
            </div>
          </div>
        );
      case 'previous-trips':
        return (
          <div className="max-w-4xl mx-auto p-8">
            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">Previous Trips</h1>
              <p className="text-gray-600">Coming soon - View your trip history</p>
            </div>
          </div>
        );
      case 'settings':
        return (
          <div className="max-w-4xl mx-auto p-8">
            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">Settings</h1>
              <p className="text-gray-600">Coming soon - Manage your preferences</p>
            </div>
          </div>
        );
      default:
        return <HomePage currentUser={currentUser} onNavigate={handleNavigation} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar
        currentUser={currentUser}
        currentPage={currentPage}
        onNavigate={handleNavigation}
        onLogout={handleLogout}
      />
      <div className="flex-1 overflow-auto">
        {renderCurrentPage()}
      </div>
    </div>
  );
}

export default App;