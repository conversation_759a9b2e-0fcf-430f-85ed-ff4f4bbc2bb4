export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: string;
}

export interface Trip {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  budget?: number;
  creatorId: string;
  code: string;
  participants: string[]; // user IDs
  createdAt: string;
}

export interface Activity {
  id: string;
  tripId: string;
  title: string;
  date: string;
  time: string;
  category: 'Adventure' | 'Food' | 'Sightseeing' | 'Other';
  estimatedCost: number;
  notes?: string;
  createdBy: string;
  votes: string[]; // user IDs who voted
  lockedIn: boolean;
  createdAt: string;
}

export interface ChatMessage {
  id: string;
  tripId: string;
  userId: string;
  message: string;
  timestamp: string;
}

export interface Budget {
  tripId: string;
  totalBudget: number;
  totalEstimated: number;
  categoryBreakdown: {
    Adventure: number;
    Food: number;
    Sightseeing: number;
    Other: number;
  };
}

export interface AppState {
  currentUser: User | null;
  isAuthenticated: boolean;
  trips: Trip[];
  activities: Activity[];
  messages: ChatMessage[];
  users: User[];
}