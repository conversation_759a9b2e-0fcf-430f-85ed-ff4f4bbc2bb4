import { User } from '../types';
import { storage } from './storage';

export const generateId = () => {
  return Math.random().toString(36).substr(2, 9);
};

export const generateTripCode = () => {
  return Math.random().toString(36).substr(2, 6).toUpperCase();
};

export const auth = {
  register: (email: string, password: string, name: string): User | null => {
    const users = storage.getUsers();
    
    // Check if user already exists
    if (users.find(u => u.email === email)) {
      return null;
    }

    const newUser: User = {
      id: generateId(),
      email,
      name,
      avatar: `https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face`,
      createdAt: new Date().toISOString()
    };

    users.push(newUser);
    storage.setUsers(users);
    storage.setCurrentUser(newUser);
    
    return newUser;
  },

  login: (email: string, password: string): User | null => {
    const users = storage.getUsers();
    const user = users.find(u => u.email === email);
    
    if (user) {
      storage.setCurrentUser(user);
      return user;
    }
    
    return null;
  },

  logout: () => {
    storage.setCurrentUser(null);
  },

  getCurrentUser: () => {
    return storage.getCurrentUser();
  }
};