// User Types
export interface User {
  _id: string;
  name: string;
  email: string;
  profilePhoto?: string;
  bio?: string;
  preferences: UserPreferences;
  trips: string[];
  friends: Friend[];
  lastActive: string;
  isActive: boolean;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  currency: Currency;
  timezone: string;
  notifications: NotificationSettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  tripUpdates: boolean;
  activityVotes: boolean;
  messages: boolean;
}

export interface Friend {
  user: User;
  status: "pending" | "accepted" | "blocked";
  addedAt: string;
}

// Trip Types
export interface Trip {
  _id: string;
  name: string;
  description?: string;
  destination?: string;
  startDate: string;
  endDate: string;
  budget: Budget;
  tripCode: string;
  creator: User;
  participants: Participant[];
  activities: string[];
  settings: TripSettings;
  status: TripStatus;
  tags: string[];
  coverImage?: string;
  lastActivity: string;
  createdAt: string;
  updatedAt: string;
  // Virtual fields
  duration?: number;
  remainingBudget?: number;
  participantCount?: number;
  activityCount?: number;
  progress?: number;
}

export interface Budget {
  total: number;
  currency: Currency;
  spent: number;
}

export interface Participant {
  user: User;
  role: ParticipantRole;
  joinedAt: string;
  status: ParticipantStatus;
  permissions: ParticipantPermissions;
}

export interface ParticipantPermissions {
  canEdit: boolean;
  canInvite: boolean;
  canDelete: boolean;
}

export interface TripSettings {
  isPublic: boolean;
  allowVoting: boolean;
  requireApproval: boolean;
  chatEnabled: boolean;
  budgetVisible: boolean;
}

// Activity Types
export interface Activity {
  _id: string;
  title: string;
  description?: string;
  trip: string;
  creator: User;
  date: string;
  time: ActivityTime;
  location: ActivityLocation;
  category: ActivityCategory;
  cost: ActivityCost;
  votes: Vote[];
  priority: Priority;
  status: ActivityStatus;
  tags: string[];
  attachments: Attachment[];
  notes?: string;
  isPublic: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
  // Virtual fields
  voteScore?: number;
  totalVotes?: number;
  upvotes?: number;
  downvotes?: number;
  totalCost?: number;
}

export interface ActivityTime {
  start?: string;
  end?: string;
}

export interface ActivityLocation {
  name?: string;
  address?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface ActivityCost {
  amount: number;
  currency: Currency;
  perPerson: boolean;
  notes?: string;
}

export interface Vote {
  user: User;
  vote: "up" | "down";
  votedAt: string;
}

export interface Attachment {
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
  uploadedBy: User;
  uploadedAt: string;
}

// Message Types
export interface Message {
  _id: string;
  trip: string;
  sender: User;
  content: string;
  type: MessageType;
  metadata?: MessageMetadata;
  readBy: MessageRead[];
  reactions: Reaction[];
  replyTo?: Message;
  edited: EditInfo;
  deleted: DeleteInfo;
  createdAt: string;
  updatedAt: string;
}
