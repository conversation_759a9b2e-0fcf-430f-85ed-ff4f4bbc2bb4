import React from 'react';
import { Calendar, Users, MapPin, Clock } from 'lucide-react';
import { Trip, User } from '../../types';

interface FriendsTripsPageProps {
  currentUser: User;
  trips: Trip[];
  users: User[];
  onNavigate: (page: string, tripId?: string) => void;
}

export const FriendsTripsPage: React.FC<FriendsTripsPageProps> = ({ 
  currentUser, 
  trips, 
  users, 
  onNavigate 
}) => {
  // Filter trips where current user is participant but not creator
  const friendsTrips = trips.filter(trip => 
    trip.participants.includes(currentUser.id) && trip.creatorId !== currentUser.id
  );

  const getTripCreator = (creatorId: string) => {
    return users.find(user => user.id === creatorId);
  };

  const getTripStatus = (trip: Trip) => {
    const now = new Date();
    const startDate = new Date(trip.startDate);
    const endDate = new Date(trip.endDate);

    if (now < startDate) return { label: 'Upcoming', color: 'text-blue-600 bg-blue-100' };
    if (now > endDate) return { label: 'Completed', color: 'text-gray-600 bg-gray-100' };
    return { label: 'Active', color: 'text-green-600 bg-green-100' };
  };

  if (friendsTrips.length === 0) {
    return (
      <div className="max-w-4xl mx-auto p-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Friends' Trips</h1>
          <p className="text-gray-600 mb-8">Trips you've been invited to join</p>

          <div className="bg-white rounded-2xl shadow-lg p-12">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gray-100 text-gray-400 rounded-full mb-6">
              <Users className="w-10 h-10" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">No trips yet</h2>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              You haven't been invited to any trips yet. Ask your friends to share their trip codes with you!
            </p>
            <button
              onClick={() => onNavigate('home')}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Go to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Friends' Trips</h1>
        <p className="text-gray-600">Trips you've been invited to join ({friendsTrips.length})</p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {friendsTrips.map((trip) => {
          const creator = getTripCreator(trip.creatorId);
          const status = getTripStatus(trip);
          
          return (
            <div
              key={trip.id}
              className="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1"
              onClick={() => onNavigate('trip-dashboard', trip.id)}
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${status.color}`}>
                  {status.label}
                </div>
                <div className="text-sm text-gray-500">
                  {trip.participants.length} {trip.participants.length === 1 ? 'person' : 'people'}
                </div>
              </div>

              <h3 className="text-xl font-semibold text-gray-900 mb-2">{trip.name}</h3>
              
              <div className="space-y-2 mb-4">
                <div className="flex items-center text-gray-600">
                  <Calendar className="w-4 h-4 mr-2" />
                  <span className="text-sm">
                    {new Date(trip.startDate).toLocaleDateString()} - {new Date(trip.endDate).toLocaleDateString()}
                  </span>
                </div>
                
                {creator && (
                  <div className="flex items-center text-gray-600">
                    <Users className="w-4 h-4 mr-2" />
                    <span className="text-sm">Organized by {creator.name}</span>
                  </div>
                )}

                {trip.budget && (
                  <div className="flex items-center text-gray-600">
                    <MapPin className="w-4 h-4 mr-2" />
                    <span className="text-sm">Budget: ${trip.budget.toLocaleString()}</span>
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                <div className="flex items-center text-gray-500">
                  <Clock className="w-4 h-4 mr-1" />
                  <span className="text-xs">
                    Created {new Date(trip.createdAt).toLocaleDateString()}
                  </span>
                </div>
                <div className="text-sm font-medium text-blue-600">
                  View Details →
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};