import React from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { Toaster } from "react-hot-toast";
import { AuthProvider } from "./context/AuthContext";
import { SocketProvider } from "./context/SocketContext";
import Layout from "./components/layout/Layout";
import Login from "./pages/auth/Login";
import Register from "./pages/auth/Register";
import Dashboard from "./pages/dashboard/Dashboard";
import ProtectedRoute from "./components/ProtectedRoute";

function App() {
  return (
    <AuthProvider>
      <SocketProvider>
        <Router>
          <div className="App">
            <Routes>
              <Route path="/" element={<Layout />}>
                {/* Public routes */}
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />

                {/* Protected routes */}
                <Route path="/dashboard" element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                } />

                {/* Redirect root to dashboard */}
                <Route path="/" element={<Navigate to="/dashboard" replace />} />

                {/* Catch all route */}
                <Route path="*" element={<Navigate to="/dashboard" replace />} />
              </Route>
            </Routes>
      case "budget-lineup":
        return (
          <div className="max-w-4xl mx-auto p-8">
            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Budget Lineup
              </h1>
              <p className="text-gray-600">
                Coming soon - Track your spending across all trips
              </p>
            </div>
          </div>
        );
      case "previous-trips":
        return (
          <div className="max-w-4xl mx-auto p-8">
            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Previous Trips
              </h1>
              <p className="text-gray-600">
                Coming soon - View your trip history
              </p>
            </div>
          </div>
        );
      case "settings":
        return (
          <div className="max-w-4xl mx-auto p-8">
            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Settings
              </h1>
              <p className="text-gray-600">
                Coming soon - Manage your preferences
              </p>
            </div>
          </div>
        );
      default:
        return (
          <HomePage currentUser={currentUser} onNavigate={handleNavigation} />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar
        currentUser={currentUser}
        currentPage={currentPage}
        onNavigate={handleNavigation}
        onLogout={handleLogout}
      />
      <div className="flex-1 overflow-auto">{renderCurrentPage()}</div>
    </div>
  );
}

export default App;
