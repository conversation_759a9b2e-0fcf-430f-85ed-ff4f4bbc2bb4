import React, { useState } from 'react';
import { LoginForm } from './LoginForm';
import { RegisterForm } from './RegisterForm';
import { MapPin } from 'lucide-react';

interface AuthScreenProps {
  onAuthenticated: (user: any) => void;
}

export const AuthScreen: React.FC<AuthScreenProps> = ({ onAuthenticated }) => {
  const [isLogin, setIsLogin] = useState(true);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
        <div className="hidden lg:block">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-blue-600 text-white rounded-2xl mb-6">
              <MapPin className="w-10 h-10" />
            </div>
            <h1 className="text-5xl font-bold text-gray-900 mb-4">
              Planit
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Collaborative Trip Planner
            </p>
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                Plan group trips without chaos
              </h3>
              <p className="text-gray-600 text-lg leading-relaxed">
                Manage budgets, activities, and decisions together in one place. 
                Collaborate with friends to create unforgettable travel experiences.
              </p>
            </div>
          </div>
        </div>

        <div className="lg:hidden text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 text-white rounded-2xl mb-4">
            <MapPin className="w-8 h-8" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Planit</h1>
          <p className="text-gray-600">Collaborative Trip Planner</p>
        </div>

        <div>
          {isLogin ? (
            <LoginForm
              onLogin={onAuthenticated}
              onSwitchToRegister={() => setIsLogin(false)}
            />
          ) : (
            <RegisterForm
              onRegister={onAuthenticated}
              onSwitchToLogin={() => setIsLogin(true)}
            />
          )}
        </div>
      </div>
    </div>
  );
};