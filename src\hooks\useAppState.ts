import { useState, useEffect } from 'react';
import { User, Trip, Activity, ChatMessage } from '../types';
import { storage } from '../utils/storage';
import { auth } from '../utils/auth';

export const useAppState = () => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [trips, setTrips] = useState<Trip[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [users, setUsers] = useState<User[]>([]);

  useEffect(() => {
    // Load initial data
    setCurrentUser(auth.getCurrentUser());
    setTrips(storage.getTrips());
    setActivities(storage.getActivities());
    setMessages(storage.getMessages());
    setUsers(storage.getUsers());
  }, []);

  const updateTrips = (newTrips: Trip[]) => {
    setTrips(newTrips);
    storage.setTrips(newTrips);
  };

  const updateActivities = (newActivities: Activity[]) => {
    setActivities(newActivities);
    storage.setActivities(newActivities);
  };

  const updateMessages = (newMessages: ChatMessage[]) => {
    setMessages(newMessages);
    storage.setMessages(newMessages);
  };

  const updateUsers = (newUsers: User[]) => {
    setUsers(newUsers);
    storage.setUsers(newUsers);
  };

  return {
    currentUser,
    setCurrentUser,
    trips,
    updateTrips,
    activities,
    updateActivities,
    messages,
    updateMessages,
    users,
    updateUsers,
    isAuthenticated: !!currentUser
  };
};