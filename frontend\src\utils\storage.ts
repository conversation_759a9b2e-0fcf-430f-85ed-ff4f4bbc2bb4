import { User, Trip, Activity, ChatMessage, AppState } from '../types';

const STORAGE_KEYS = {
  CURRENT_USER: 'planit_current_user',
  USERS: 'planit_users',
  TRIPS: 'planit_trips',
  ACTIVITIES: 'planit_activities',
  MESSAGES: 'planit_messages',
  LAST_VISITED_TRIP: 'planit_last_visited_trip',
  UI_PREFERENCES: 'planit_ui_preferences'
};

export const storage = {
  // User management
  getCurrentUser: (): User | null => {
    const user = localStorage.getItem(STORAGE_KEYS.CURRENT_USER);
    return user ? JSON.parse(user) : null;
  },

  setCurrentUser: (user: User | null) => {
    if (user) {
      localStorage.setItem(STORAGE_KEYS.CURRENT_USER, JSON.stringify(user));
    } else {
      localStorage.removeItem(STORAGE_KEYS.CURRENT_USER);
    }
  },

  getUsers: (): User[] => {
    const users = localStorage.getItem(STORAGE_KEYS.USERS);
    return users ? JSON.parse(users) : [];
  },

  setUsers: (users: User[]) => {
    localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));
  },

  // Trip management
  getTrips: (): Trip[] => {
    const trips = localStorage.getItem(STORAGE_KEYS.TRIPS);
    return trips ? JSON.parse(trips) : [];
  },

  setTrips: (trips: Trip[]) => {
    localStorage.setItem(STORAGE_KEYS.TRIPS, JSON.stringify(trips));
  },

  // Activity management
  getActivities: (): Activity[] => {
    const activities = localStorage.getItem(STORAGE_KEYS.ACTIVITIES);
    return activities ? JSON.parse(activities) : [];
  },

  setActivities: (activities: Activity[]) => {
    localStorage.setItem(STORAGE_KEYS.ACTIVITIES, JSON.stringify(activities));
  },

  // Message management
  getMessages: (): ChatMessage[] => {
    const messages = localStorage.getItem(STORAGE_KEYS.MESSAGES);
    return messages ? JSON.parse(messages) : [];
  },

  setMessages: (messages: ChatMessage[]) => {
    localStorage.setItem(STORAGE_KEYS.MESSAGES, JSON.stringify(messages));
  },

  // Last visited trip
  getLastVisitedTrip: (): string | null => {
    return localStorage.getItem(STORAGE_KEYS.LAST_VISITED_TRIP);
  },

  setLastVisitedTrip: (tripId: string) => {
    localStorage.setItem(STORAGE_KEYS.LAST_VISITED_TRIP, tripId);
  },

  // UI Preferences
  getUIPreferences: () => {
    const prefs = localStorage.getItem(STORAGE_KEYS.UI_PREFERENCES);
    return prefs ? JSON.parse(prefs) : {
      darkMode: false,
      hidePastDates: false,
      onlyLockedIn: false
    };
  },

  setUIPreferences: (prefs: any) => {
    localStorage.setItem(STORAGE_KEYS.UI_PREFERENCES, JSON.stringify(prefs));
  }
};