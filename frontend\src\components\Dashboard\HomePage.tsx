import React from 'react';
import { Plus, Users, MapPin, Calendar, DollarSign } from 'lucide-react';
import { User } from '../../types';

interface HomePageProps {
  currentUser: User;
  onNavigate: (page: string) => void;
}

export const HomePage: React.FC<HomePageProps> = ({ currentUser, onNavigate }) => {
  return (
    <div className="max-w-4xl mx-auto p-8">
      {/* Welcome Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Welcome back, {currentUser.name.split(' ')[0]}! 👋
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
          Plan group trips without chaos — manage budgets, activities, and decisions together in one place.
        </p>
      </div>

      {/* Main Actions */}
      <div className="grid md:grid-cols-2 gap-8 mb-12">
        <button
          onClick={() => onNavigate('create-trip')}
          className="group bg-gradient-to-br from-blue-500 to-blue-600 text-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
        >
          <div className="flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
            <Plus className="w-8 h-8" />
          </div>
          <h2 className="text-2xl font-bold mb-2">Create New Trip</h2>
          <p className="text-blue-100">Start planning your next adventure with friends</p>
        </button>

        <button
          onClick={() => onNavigate('friends-trips')}
          className="group bg-gradient-to-br from-emerald-500 to-emerald-600 text-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
        >
          <div className="flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
            <Users className="w-8 h-8" />
          </div>
          <h2 className="text-2xl font-bold mb-2">View Friends' Trips</h2>
          <p className="text-emerald-100">See trips you've been invited to join</p>
        </button>
      </div>

      {/* Feature Highlights */}
      <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
        <h3 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
          What makes Planit special?
        </h3>
        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 text-purple-600 rounded-full mb-4">
              <MapPin className="w-6 h-6" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">Collaborative Planning</h4>
            <p className="text-gray-600">Plan activities together and vote on your favorites</p>
          </div>
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-orange-100 text-orange-600 rounded-full mb-4">
              <DollarSign className="w-6 h-6" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">Budget Management</h4>
            <p className="text-gray-600">Track expenses and stay within your group budget</p>
          </div>
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 text-blue-600 rounded-full mb-4">
              <Calendar className="w-6 h-6" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">Smart Scheduling</h4>
            <p className="text-gray-600">Organize activities by date and time automatically</p>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl text-center">
          <div className="text-2xl font-bold text-blue-600 mb-1">0</div>
          <div className="text-sm text-blue-600">Active Trips</div>
        </div>
        <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 p-6 rounded-xl text-center">
          <div className="text-2xl font-bold text-emerald-600 mb-1">0</div>
          <div className="text-sm text-emerald-600">Friends</div>
        </div>
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl text-center">
          <div className="text-2xl font-bold text-purple-600 mb-1">0</div>
          <div className="text-sm text-purple-600">Activities</div>
        </div>
        <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-6 rounded-xl text-center">
          <div className="text-2xl font-bold text-orange-600 mb-1">$0</div>
          <div className="text-sm text-orange-600">Total Budget</div>
        </div>
      </div>
    </div>
  );
};